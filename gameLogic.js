// Game Logic for Parent-Child Idiom Competition
class GameLogic {
  constructor() {
    this.gameState = {
      isPlaying: false,
      startTime: null,
      timeRemaining: gameData.settings.timeLimit,
      players: {
        child: {
          score: 0,
          currentQuestion: 0,
          questions: [],
          isFinished: false,
          completionTime: null,
          answers: [],
        },
        parent: {
          score: 0,
          currentQuestion: 0,
          questions: [],
          isFinished: false,
          completionTime: null,
          answers: [],
        },
      },
    };

    this.timer = null;
    this.callbacks = {
      onTimeUpdate: null,
      onScoreUpdate: null,
      onQuestionUpdate: null,
      onGameEnd: null,
    };
  }

  // Initialize game with random questions for each player
  initializeGame() {
    // Get different sets of questions for each player
    const allQuestions = gameDataUtils.shuffleArray(gameData.idioms);
    const questionsPerPlayer = Math.ceil(allQuestions.length / 2);

    this.gameState.players.child.questions = allQuestions.slice(
      0,
      questionsPerPlayer
    );
    this.gameState.players.parent.questions =
      allQuestions.slice(questionsPerPlayer);

    // Reset game state
    this.gameState.players.child.score = 0;
    this.gameState.players.child.currentQuestion = 0;
    this.gameState.players.child.isFinished = false;
    this.gameState.players.child.completionTime = null;
    this.gameState.players.child.answers = [];

    this.gameState.players.parent.score = 0;
    this.gameState.players.parent.currentQuestion = 0;
    this.gameState.players.parent.isFinished = false;
    this.gameState.players.parent.completionTime = null;
    this.gameState.players.parent.answers = [];

    this.gameState.timeRemaining = gameData.settings.timeLimit;
    this.gameState.isPlaying = false;
  }

  // Start the game
  startGame() {
    this.gameState.isPlaying = true;
    this.gameState.startTime = Date.now();
    // 总时间倒计时
    // this.startTimer();

    // Load first questions for both players
    this.loadQuestion("child");
    this.loadQuestion("parent");
  }

  // Start the countdown timer
  startTimer() {
    this.timer = setInterval(() => {
      this.gameState.timeRemaining--;

      if (this.callbacks.onTimeUpdate) {
        this.callbacks.onTimeUpdate(this.gameState.timeRemaining);
      }

      if (this.gameState.timeRemaining <= 0) {
        this.endGame();
      }
    }, 1000);
  }

  // Stop the timer
  stopTimer() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }

  // Load current question for a player
  loadQuestion(playerType) {
    const player = this.gameState.players[playerType];

    if (player.currentQuestion >= player.questions.length) {
      this.finishPlayer(playerType);
      return;
    }

    const question = player.questions[player.currentQuestion];
    const shuffledArtifacts = gameDataUtils.getShuffledArtifacts(question);

    if (this.callbacks.onQuestionUpdate) {
      this.callbacks.onQuestionUpdate(playerType, question, shuffledArtifacts);
    }
  }

  // Handle player answer
  submitAnswer(playerType, selectedChar) {
    const player = this.gameState.players[playerType];
    const question = player.questions[player.currentQuestion];
    const isCorrect = selectedChar === question.missingChar;

    // Record the answer
    player.answers.push({
      question: question,
      selectedChar: selectedChar,
      isCorrect: isCorrect,
      timestamp: Date.now() - this.gameState.startTime,
    });

    // Update score if correct
    if (isCorrect) {
      player.score += gameData.settings.pointsPerCorrect;
    }

    // Update UI
    if (this.callbacks.onScoreUpdate) {
      this.callbacks.onScoreUpdate(
        playerType,
        player.score,
        isCorrect,
        question.missingChar
      );
    }

    // Move to next question after a delay
    setTimeout(() => {
      player.currentQuestion++;
      this.loadQuestion(playerType);
    }, 2000);
  }

  // Mark player as finished
  finishPlayer(playerType) {
    const player = this.gameState.players[playerType];
    player.isFinished = true;
    player.completionTime = Date.now() - this.gameState.startTime;

    // Check if both players are finished
    if (
      this.gameState.players.child.isFinished &&
      this.gameState.players.parent.isFinished
    ) {
      this.endGame();
    }
  }

  // End the game
  endGame() {
    this.gameState.isPlaying = false;
    this.stopTimer();

    // Mark any unfinished players
    Object.keys(this.gameState.players).forEach((playerType) => {
      const player = this.gameState.players[playerType];
      if (!player.isFinished) {
        player.completionTime = Date.now() - this.gameState.startTime;
        player.isFinished = true;
      }
    });

    if (this.callbacks.onGameEnd) {
      this.callbacks.onGameEnd(this.getGameResults());
    }
  }

  // Get game results
  getGameResults() {
    const childPlayer = this.gameState.players.child;
    const parentPlayer = this.gameState.players.parent;

    // Determine winner
    let winner = null;
    if (childPlayer.score > parentPlayer.score) {
      winner = "child";
    } else if (parentPlayer.score > childPlayer.score) {
      winner = "parent";
    } else {
      // Tie-breaker: faster completion time
      if (childPlayer.completionTime < parentPlayer.completionTime) {
        winner = "child";
      } else {
        winner = "parent";
      }
    }

    return {
      winner: winner,
      child: {
        score: childPlayer.score,
        completionTime: childPlayer.completionTime,
        answers: childPlayer.answers,
        badge: winner === "child" ? "sun" : "moon",
      },
      parent: {
        score: parentPlayer.score,
        completionTime: parentPlayer.completionTime,
        answers: parentPlayer.answers,
        badge: winner === "parent" ? "sun" : "moon",
      },
      allQuestions: [...childPlayer.questions, ...parentPlayer.questions],
    };
  }

  // Get current game state
  getGameState() {
    return this.gameState;
  }

  // Set callback functions
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  // Get progress percentage for a player
  getProgress(playerType) {
    const player = this.gameState.players[playerType];
    return Math.min(
      (player.currentQuestion / player.questions.length) * 100,
      100
    );
  }

  // Get current question for a player
  getCurrentQuestion(playerType) {
    const player = this.gameState.players[playerType];
    if (player.currentQuestion < player.questions.length) {
      return player.questions[player.currentQuestion];
    }
    return null;
  }

  // Check if game is over
  isGameOver() {
    return (
      !this.gameState.isPlaying ||
      (this.gameState.players.child.isFinished &&
        this.gameState.players.parent.isFinished) ||
      this.gameState.timeRemaining <= 0
    );
  }

  // Reset game
  reset() {
    this.stopTimer();
    this.gameState = {
      isPlaying: false,
      startTime: null,
      timeRemaining: gameData.settings.timeLimit,
      players: {
        child: {
          score: 0,
          currentQuestion: 0,
          questions: [],
          isFinished: false,
          completionTime: null,
          answers: [],
        },
        parent: {
          score: 0,
          currentQuestion: 0,
          questions: [],
          isFinished: false,
          completionTime: null,
          answers: [],
        },
      },
    };
  }
}

// Export for use in other files
window.GameLogic = GameLogic;
