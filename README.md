# 邯郸文化成语竞赛 - 亲子PK游戏

一个基于HTML5、JavaScript和CSS的移动端网页应用，专为亲子成语竞赛设计，融合邯郸文化文物元素。

## 游戏特色

### 🎮 游戏模式
- **亲子PK模式**：家长和孩子同时竞赛
- **分屏设计**：水平分屏布局，支持面对面游戏
- **屏幕翻转**：答题区域自动翻转，双方都能正向查看

### 🏛️ 文化内容
- **邯郸成语**：精选与邯郸历史文化相关的经典成语
- **文物图片**：通过文物图片选择正确的成语字符
- **教育价值**：游戏结束后展示成语释义和历史故事

### 🎨 视觉设计
- **儿童区域**：可爱、彩色的设计风格
- **家长区域**：经典、复古的设计风格
- **响应式设计**：完美适配移动设备

## 游戏玩法

### 开始界面
- 显示游戏规则和操作说明
- 点击"开始游戏"按钮进入竞赛

### 游戏过程
1. **题目显示**：显示不完整的成语，缺少一个字符
2. **选择答案**：从邯郸文物图片中选择正确的字符
3. **即时反馈**：答对得分，答错显示正确答案
4. **进度追踪**：实时显示双方得分和进度
5. **计时功能**：5分钟倒计时增加紧张感

### 结果展示
- **最终得分**：显示双方得分和用时
- **徽章奖励**：胜者获得太阳徽章，败者获得月亮徽章
- **教育内容**：展示所有成语的详细解释和文化故事

## 技术特性

### 前端技术
- **HTML5**：语义化标记和现代Web标准
- **CSS3**：渐变、动画、响应式布局
- **JavaScript ES6+**：模块化设计和现代语法

### 移动优化
- **触摸友好**：大按钮和手势支持
- **响应式设计**：适配各种屏幕尺寸
- **性能优化**：流畅的动画和交互

### 音效系统
- **Web Audio API**：原生音效支持
- **反馈音效**：正确/错误答案的声音提示

## 文件结构

```
demo-1/
├── index.html          # 主HTML文件
├── styles.css          # 样式表
├── gameData.js         # 游戏数据和成语库
├── gameLogic.js        # 游戏逻辑核心
├── app.js             # 主应用程序
└── README.md          # 项目说明
```

## 使用方法

1. **直接打开**：在浏览器中打开 `index.html`
2. **本地服务器**：推荐使用本地HTTP服务器运行
3. **移动设备**：在移动浏览器中访问获得最佳体验

## 游戏数据

### 成语库
包含5个经典邯郸相关成语：
- 邯郸学步
- 完璧归赵
- 胡服骑射
- 负荆请罪
- 围魏救赵

### 文物元素
每个成语配有相关的邯郸文化文物图片，增强教育价值和文化认同感。

## 浏览器兼容性

- **现代浏览器**：Chrome、Firefox、Safari、Edge
- **移动浏览器**：iOS Safari、Android Chrome
- **最低要求**：支持ES6和CSS3的浏览器

## 开发说明

### 扩展游戏内容
1. 在 `gameData.js` 中添加新的成语和文物数据
2. 确保每个成语包含完整的故事和解释
3. 提供高质量的文物图片（建议使用SVG或高分辨率图片）

### 自定义样式
1. 修改 `styles.css` 中的颜色和布局
2. 调整响应式断点以适配特定设备
3. 添加新的动画效果

### 功能扩展
1. 添加更多游戏模式
2. 实现分数保存功能
3. 增加社交分享功能

## 许可证

本项目为教育用途开发，遵循开源精神。

## 贡献

欢迎提交问题报告和功能建议，共同完善这个教育游戏。
