// Main Application for Parent-Child Idiom Competition
class IdiomCompetitionApp {
  constructor() {
    this.gameLogic = new GameLogic();
    this.currentScreen = "start";
    this.elements = {};
    this.init();
  }

  // Initialize the application
  init() {
    this.cacheElements();
    this.bindEvents();
    this.setupGameCallbacks();
    this.setupSoundEffects();
    this.showScreen("start");
  }

  // Setup sound effects (using Web Audio API for simple sounds)
  setupSoundEffects() {
    this.audioContext = null;
    try {
      this.audioContext = new (window.AudioContext ||
        window.webkitAudioContext)();
    } catch (e) {
      console.log("Web Audio API not supported");
    }
  }

  // Play a simple beep sound
  playSound(frequency = 440, duration = 200, type = "sine") {
    if (!this.audioContext) return;

    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    oscillator.frequency.value = frequency;
    oscillator.type = type;

    gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(
      0.01,
      this.audioContext.currentTime + duration / 1000
    );

    oscillator.start(this.audioContext.currentTime);
    oscillator.stop(this.audioContext.currentTime + duration / 1000);
  }

  // Cache DOM elements
  cacheElements() {
    // Screens
    this.elements.startScreen = document.getElementById("startScreen");
    this.elements.gameScreen = document.getElementById("gameScreen");
    this.elements.resultsScreen = document.getElementById("resultsScreen");

    // Start screen elements
    this.elements.startGameBtn = document.getElementById("startGameBtn");

    // Game screen elements
    this.elements.gameTimer = document.getElementById("gameTimer");
    this.elements.childProgress = document.getElementById("childProgress");
    this.elements.parentProgress = document.getElementById("parentProgress");
    this.elements.childScoreText = document.getElementById("childScoreText");
    this.elements.parentScoreText = document.getElementById("parentScoreText");

    // Child section elements
    this.elements.childIdiom = document.getElementById("childIdiom");
    this.elements.childMissing = document.getElementById("childMissing");
    this.elements.childArtifacts = document.getElementById("childArtifacts");
    this.elements.childFeedback = document.getElementById("childFeedback");

    // Parent section elements
    this.elements.parentIdiom = document.getElementById("parentIdiom");
    this.elements.parentMissing = document.getElementById("parentMissing");
    this.elements.parentArtifacts = document.getElementById("parentArtifacts");
    this.elements.parentFeedback = document.getElementById("parentFeedback");

    // Results screen elements
    this.elements.childBadge = document.getElementById("childBadge");
    this.elements.parentBadge = document.getElementById("parentBadge");
    this.elements.childFinalScore = document.getElementById("childFinalScore");
    this.elements.parentFinalScore =
      document.getElementById("parentFinalScore");
    this.elements.childTime = document.getElementById("childTime");
    this.elements.parentTime = document.getElementById("parentTime");
    this.elements.educationalContent =
      document.getElementById("educationalContent");
    this.elements.playAgainBtn = document.getElementById("playAgainBtn");
  }

  // Bind event listeners
  bindEvents() {
    this.elements.startGameBtn.addEventListener("click", () =>
      this.startGame()
    );
    this.elements.playAgainBtn.addEventListener("click", () =>
      this.resetGame()
    );
  }

  // Setup game logic callbacks
  setupGameCallbacks() {
    this.gameLogic.setCallbacks({
      onTimeUpdate: (timeRemaining) => this.updateTimer(timeRemaining),
      onScoreUpdate: (playerType, score, isCorrect, correctChar) =>
        this.updateScore(playerType, score, isCorrect, correctChar),
      onQuestionUpdate: (playerType, question, artifacts) =>
        this.updateQuestion(playerType, question, artifacts),
      onGameEnd: (results) => this.showResults(results),
    });
  }

  // Show specific screen
  showScreen(screenName) {
    // Hide all screens
    document.querySelectorAll(".screen").forEach((screen) => {
      screen.classList.remove("active");
    });

    // Show target screen
    const targetScreen = document.getElementById(screenName + "Screen");
    if (targetScreen) {
      targetScreen.classList.add("active");
      this.currentScreen = screenName;
    }
  }

  // Start the game
  startGame() {
    this.gameLogic.initializeGame();
    this.showScreen("game");
    this.gameLogic.startGame();
  }

  // Update timer display
  updateTimer(timeRemaining) {
    this.elements.gameTimer.textContent =
      gameDataUtils.formatTime(timeRemaining);

    // Add warning class when time is low
    if (timeRemaining <= 60) {
      this.elements.gameTimer.style.color = "#ff4444";
    } else {
      this.elements.gameTimer.style.color = "#333";
    }
  }

  // Update score and progress
  updateScore(playerType, score, isCorrect, correctChar) {
    const progress = this.gameLogic.getProgress(playerType);
    const totalQuestions =
      this.gameLogic.getGameState().players[playerType].questions.length;
    const currentQ =
      this.gameLogic.getGameState().players[playerType].currentQuestion;

    // Play sound effect
    if (isCorrect) {
      this.playSound(523, 300, "sine"); // C note for correct
    } else {
      this.playSound(220, 500, "sawtooth"); // Lower note for incorrect
    }

    // Update progress bar
    this.elements[playerType + "Progress"].style.width = progress + "%";

    // Update score text
    this.elements[
      playerType + "ScoreText"
    ].textContent = `${currentQ}/${totalQuestions}`;

    // Show feedback
    const feedbackElement = this.elements[playerType + "Feedback"];
    if (isCorrect) {
      feedbackElement.textContent = "正确！";
      feedbackElement.className = "feedback correct";
    } else {
      feedbackElement.textContent = `错误！正确答案是：${correctChar}`;
      feedbackElement.className = "feedback incorrect";
    }

    // Clear feedback after delay
    setTimeout(() => {
      feedbackElement.textContent = "";
      feedbackElement.className = "feedback";
    }, 2000);
  }

  // Update question display
  updateQuestion(playerType, question, artifacts) {
    // Update idiom display
    const idiomElement = this.elements[playerType + "Idiom"];
    const missingElement = this.elements[playerType + "Missing"];

    // Clear previous idiom
    idiomElement.innerHTML = "";

    // Build idiom display
    const chars = question.idiom.split("");
    chars.forEach((char, index) => {
      const span = document.createElement("span");
      span.className = "idiom-char";

      if (index === question.missingIndex) {
        span.className += " missing";
        span.textContent = "?";
      } else {
        span.textContent = char;
      }

      idiomElement.appendChild(span);
    });

    // Update artifacts grid
    const artifactsElement = this.elements[playerType + "Artifacts"];
    artifactsElement.innerHTML = "";

    artifacts.forEach((artifact) => {
      const artifactDiv = document.createElement("div");
      artifactDiv.className = "artifact-option";
      artifactDiv.innerHTML = `
                <img src="${artifact.image}" alt="${artifact.description}" class="artifact-image">
                <div class="artifact-char">${artifact.char}</div>
            `;

      artifactDiv.addEventListener("click", () => {
        this.selectArtifact(playerType, artifact.char, artifactDiv);
      });

      artifactsElement.appendChild(artifactDiv);
    });
  }

  // Handle artifact selection
  selectArtifact(playerType, selectedChar, artifactElement) {
    // Disable all artifacts for this player temporarily
    const allArtifacts =
      this.elements[playerType + "Artifacts"].querySelectorAll(
        ".artifact-option"
      );
    allArtifacts.forEach((artifact) => {
      artifact.style.pointerEvents = "none";
    });

    // Get the correct answer for visual feedback
    const currentQuestion = this.gameLogic.getCurrentQuestion(playerType);
    const isCorrect = selectedChar === currentQuestion.missingChar;

    // Highlight selected artifact
    if (isCorrect) {
      artifactElement.classList.add("selected");
    } else {
      artifactElement.classList.add("wrong");
      // Also highlight the correct answer
      allArtifacts.forEach((artifact) => {
        const artifactChar =
          artifact.querySelector(".artifact-char").textContent;
        if (artifactChar === currentQuestion.missingChar) {
          artifact.classList.add("selected");
        }
      });
    }

    // Submit answer
    this.gameLogic.submitAnswer(playerType, selectedChar);

    // Re-enable artifacts after delay (for next question)
    setTimeout(() => {
      allArtifacts.forEach((artifact) => {
        artifact.style.pointerEvents = "auto";
        artifact.classList.remove("selected", "wrong");
      });
    }, 2500);
  }

  // Show game results
  showResults(results) {
    this.showScreen("results");

    // Update badges
    this.elements.childBadge.innerHTML =
      gameData.badges[results.child.badge].emoji;
    this.elements.childBadge.className = `badge ${results.child.badge}`;
    this.elements.parentBadge.innerHTML =
      gameData.badges[results.parent.badge].emoji;
    this.elements.parentBadge.className = `badge ${results.parent.badge}`;

    // Update scores
    this.elements.childFinalScore.textContent = `${results.child.score}分`;
    this.elements.parentFinalScore.textContent = `${results.parent.score}分`;

    // Update completion times
    this.elements.childTime.textContent = `用时: ${gameDataUtils.formatTime(
      Math.floor(results.child.completionTime / 1000)
    )}`;
    this.elements.parentTime.textContent = `用时: ${gameDataUtils.formatTime(
      Math.floor(results.parent.completionTime / 1000)
    )}`;

    // Generate educational content
    this.generateEducationalContent(results.allQuestions);
  }

  // Generate educational content for results screen
  generateEducationalContent(questions) {
    let content = "<h3>成语知识</h3>";

    // Get unique questions (remove duplicates)
    const uniqueQuestions = questions.filter(
      (question, index, self) =>
        index === self.findIndex((q) => q.id === question.id)
    );

    uniqueQuestions.forEach((question) => {
      content += `
                <div style="margin-bottom: 1.5rem; padding: 1rem; background: white; border-radius: 10px;">
                    <h4 style="color: #333; margin-bottom: 0.5rem;">${question.idiom}</h4>
                    <p style="margin-bottom: 0.5rem;"><strong>释义：</strong>${question.explanation}</p>
                    <p style="font-size: 0.9rem; color: #666; line-height: 1.4;">${question.story}</p>
                </div>
            `;
    });

    this.elements.educationalContent.innerHTML = content;
  }

  // Reset game
  resetGame() {
    this.gameLogic.reset();
    this.showScreen("start");

    // Clear any lingering feedback
    this.elements.childFeedback.textContent = "";
    this.elements.childFeedback.className = "feedback";
    this.elements.parentFeedback.textContent = "";
    this.elements.parentFeedback.className = "feedback";
  }
}

// Initialize the application when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.app = new IdiomCompetitionApp();
});
