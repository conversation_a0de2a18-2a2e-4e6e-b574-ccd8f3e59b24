// Main Application for Parent-Child Idiom Competition
class IdiomCompetitionApp {
  constructor() {
    this.gameLogic = new GameLogic();
    this.currentScreen = "start";
    this.elements = {};
    this.init();
  }

  // Initialize the application
  init() {
    this.cacheElements();
    this.bindEvents();
    this.setupGameCallbacks();
    this.showScreen("start");
  }

  // Setup sound effects (using Web Audio API for simple sounds)
  setupSoundEffects() {
    this.audioContext = null;
    try {
      this.audioContext = new (window.AudioContext ||
        window.webkitAudioContext)();
    } catch (e) {
      console.log("Web Audio API not supported");
    }
  }

  // Cache DOM elements
  cacheElements() {
    // Screens
    this.elements.startScreen = document.getElementById("startScreen");
    this.elements.gameScreen = document.getElementById("gameScreen");
    this.elements.resultsScreen = document.getElementById("resultsScreen");

    // Start screen elements
    this.elements.startGameBtn = document.getElementById("startGameBtn");

    // Game screen elements
    this.elements.childPkProgress = document.getElementById("childPkProgress");
    this.elements.parentPkProgress =
      document.getElementById("parentPkProgress");
    this.elements.childScoreText = document.getElementById("childScoreText");
    this.elements.parentScoreText = document.getElementById("parentScoreText");

    // Child section elements
    this.elements.childIdiom = document.getElementById("childIdiom");
    this.elements.childMissing = document.getElementById("childMissing");
    this.elements.childArtifacts = document.getElementById("childArtifacts");

    // Parent section elements
    this.elements.parentIdiom = document.getElementById("parentIdiom");
    this.elements.parentMissing = document.getElementById("parentMissing");
    this.elements.parentArtifacts = document.getElementById("parentArtifacts");

    // Results screen elements
    this.elements.childBadge = document.getElementById("childBadge");
    this.elements.parentBadge = document.getElementById("parentBadge");
    this.elements.childFinalScore = document.getElementById("childFinalScore");
    this.elements.parentFinalScore =
      document.getElementById("parentFinalScore");
    this.elements.childTime = document.getElementById("childTime");
    this.elements.parentTime = document.getElementById("parentTime");
    this.elements.educationalContent =
      document.getElementById("educationalContent");
    this.elements.playAgainBtn = document.getElementById("playAgainBtn");
  }

  // Bind event listeners
  bindEvents() {
    this.elements.startGameBtn.addEventListener("click", () =>
      this.startGame()
    );
    this.elements.playAgainBtn.addEventListener("click", () =>
      this.resetGame()
    );
  }

  // Setup game logic callbacks
  setupGameCallbacks() {
    this.gameLogic.setCallbacks({
      onScoreUpdate: (playerType, score, isCorrect, correctChar) =>
        this.updateScore(playerType, score, isCorrect, correctChar),
      onQuestionUpdate: (playerType, question, artifacts) =>
        this.updateQuestion(playerType, question, artifacts),
      onGameEnd: (results) => this.showResults(results),
    });
  }

  // Show specific screen
  showScreen(screenName) {
    // Hide all screens
    document.querySelectorAll(".screen").forEach((screen) => {
      screen.classList.remove("active");
    });

    // Show target screen
    const targetScreen = document.getElementById(screenName + "Screen");
    if (targetScreen) {
      targetScreen.classList.add("active");
      this.currentScreen = screenName;
    }
  }

  // Start the game
  startGame() {
    this.gameLogic.initializeGame();
    this.showScreen("game");
    this.initializePkProgress();
    this.gameLogic.startGame();
  }

  // Initialize PK progress bar to 50-50 split
  initializePkProgress() {
    this.elements.childPkProgress.style.width = "50%";
    this.elements.parentPkProgress.style.width = "50%";
  }

  // Update score and progress
  updateScore(playerType, score, isCorrect, correctChar) {
    const gameState = this.gameLogic.getGameState();
    // Update PK progress bar based on correct answers ratio
    this.updatePkProgress();
  }

  // Update PK progress bar based on correct answers ratio
  updatePkProgress() {
    const gameState = this.gameLogic.getGameState();
    const childScore = gameState.players.child.score;
    const parentScore = gameState.players.parent.score;
    const totalScore = childScore + parentScore;

    let childPercentage = 50; // Default to 50-50 split
    let parentPercentage = 50;

    if (totalScore > 0) {
      childPercentage = (childScore / totalScore) * 100;
      parentPercentage = (parentScore / totalScore) * 100;
    }

    // Update progress bar widths
    this.elements.childPkProgress.style.width = childPercentage + "%";
    this.elements.parentPkProgress.style.width = parentPercentage + "%";
  }

  // Update question display
  updateQuestion(playerType, question, artifacts) {
    // Update idiom display
    const idiomElement = this.elements[playerType + "Idiom"];
    const missingElement = this.elements[playerType + "Missing"];

    // Clear previous idiom
    idiomElement.innerHTML = "";

    // Build idiom display
    const chars = question.idiom.split("");
    chars.forEach((char) => {
      const span = document.createElement("span");
      span.className = "idiom-char";

      if (char === question.missingChar) {
        span.className += " missing";
        // 空白区域
        // span.textContent = "?";
      } else {
        span.textContent = char;
      }

      idiomElement.appendChild(span);
    });

    // Update artifacts grid
    const artifactsElement = this.elements[playerType + "Artifacts"];
    artifactsElement.innerHTML = "";

    artifacts.forEach((artifact) => {
      const artifactDiv = document.createElement("div");
      artifactDiv.className = "artifact-option";
      artifactDiv.innerHTML = `
                <img src="${artifact.image}" alt="${artifact.description}" class="artifact-image">
            `;

      artifactDiv.addEventListener("click", () => {
        this.selectArtifact(playerType, artifact.char, artifactDiv);
      });

      artifactsElement.appendChild(artifactDiv);
    });
  }

  // Handle artifact selection
  selectArtifact(playerType, selectedChar, artifactElement) {
    // Disable all artifacts for this player temporarily
    const allArtifacts =
      this.elements[playerType + "Artifacts"].querySelectorAll(
        ".artifact-option"
      );
    allArtifacts.forEach((artifact) => {
      artifact.style.pointerEvents = "none";
    });

    // Get the correct answer for visual feedback
    const currentQuestion = this.gameLogic.getCurrentQuestion(playerType);
    const isCorrect = selectedChar === currentQuestion.missingChar;
    console.log("playerType", playerType);
    console.log("selectedChar", selectedChar);
    console.log("currentQuestion", currentQuestion);
    console.log("artifactElement", artifactElement);
    console.log("isCorrect", isCorrect);
    // Highlight selected artifact
    if (isCorrect) {
      artifactElement.classList.add("selected");
    } else {
      artifactElement.classList.add("wrong");
    }

    // Submit answer
    this.gameLogic.submitAnswer(playerType, selectedChar);

    // Re-enable artifacts after delay (for next question)
    setTimeout(() => {
      allArtifacts.forEach((artifact) => {
        artifact.style.pointerEvents = "auto";
        artifact.classList.remove("selected", "wrong");
      });
    }, 2500);
  }

  // Show game results
  showResults(results) {
    this.showScreen("results");

    // Update badges
    this.elements.childBadge.innerHTML =
      gameData.badges[results.child.badge].emoji;
    this.elements.childBadge.className = `badge ${results.child.badge}`;
    this.elements.parentBadge.innerHTML =
      gameData.badges[results.parent.badge].emoji;
    this.elements.parentBadge.className = `badge ${results.parent.badge}`;

    // Update scores
    this.elements.childFinalScore.textContent = `${results.child.score}分`;
    this.elements.parentFinalScore.textContent = `${results.parent.score}分`;

    // Generate educational content
    this.generateEducationalContent(results.allQuestions);
  }

  // Generate educational content for results screen
  generateEducationalContent(questions) {
    let content = "<h3>成语知识</h3>";

    // Get unique questions (remove duplicates)
    const uniqueQuestions = questions.filter(
      (question, index, self) =>
        index === self.findIndex((q) => q.id === question.id)
    );

    uniqueQuestions.forEach((question) => {
      content += `
                <div style="margin-bottom: 1.5rem; padding: 1rem; background: white; border-radius: 10px;">
                    <h4 style="color: #333; margin-bottom: 0.5rem;">${question.idiom}</h4>
                    <p style="margin-bottom: 0.5rem;"><strong>释义：</strong>${question.explanation}</p>
                    <p style="font-size: 0.9rem; color: #666; line-height: 1.4;">${question.story}</p>
                </div>
            `;
    });

    this.elements.educationalContent.innerHTML = content;
  }

  // Reset game
  resetGame() {
    this.gameLogic.reset();
    this.showScreen("start");
  }
}

// Initialize the application when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.app = new IdiomCompetitionApp();
});
