// Game Data for Handan Cultural Idiom Competition
const gameData = {
  // Idiom questions with Handan cultural artifacts
  idioms: [
    {
      id: 1,
      idiom: "邯郸学步",
      missingChar: "郸",
      story:
        "邯郸学步的故事发生在战国时期的邯郸。一个燕国人听说邯郸人走路姿势很美，就来到邯郸学习走路。结果不但没学会邯郸人的步法，连自己原来的走法也忘了，最后只好爬着回去。这个成语比喻模仿别人不成，反而失去了自己原有的技能。",
      explanation: "比喻模仿别人不成，反而失去了自己原有的技能。",
      artifacts: [
        {
          char: "郸",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "邯郸古城遗址",
        },
        {
          char: "单",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "单独的器物",
        },
        {
          char: "步",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "脚印文物",
        },
        {
          char: "丹",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "丹砂器皿",
        },
      ],
    },
    {
      id: 2,
      idiom: "完璧归赵",
      missingChar: "璧",
      story:
        "战国时期，赵国得到了一块珍贵的和氏璧。秦王听说后，表示愿意用十五座城池来换取这块璧。赵王派蔺相如带着和氏璧到秦国。蔺相如见秦王没有诚意交换城池，便机智地将和氏璧完整地带回了赵国。",
      explanation: "比喻把原物完好无损地归还给物主。",
      artifacts: [
        {
          char: "璧",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "和氏璧",
        },
        {
          char: "壁",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "城墙砖块",
        },
        {
          char: "碧",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "碧绿宝石",
        },
        {
          char: "辟",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "辟邪器物",
        },
      ],
    },
    {
      id: 3,
      idiom: "胡服骑射",
      missingIndex: 2,
      missingChar: "骑",
      story:
        "战国时期，赵武灵王为了增强军事实力，决定学习胡人的服装和骑马射箭的技术。他下令全国改穿胡服，学习骑马射箭，这一改革大大增强了赵国的军事实力。",
      explanation: "指学习外族的长处来改进自己的不足。",
      artifacts: [
        {
          char: "骑",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "骑兵俑",
        },
        {
          char: "奇",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "奇特器物",
        },
        {
          char: "起",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "台基文物",
        },
        {
          char: "棋",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "棋子文物",
        },
      ],
    },
    {
      id: 4,
      idiom: "负荆请罪",
      missingChar: "荆",
      story:
        "战国时期，赵国大将廉颇因为嫉妒蔺相如的地位而处处为难他。后来廉颇得知蔺相如一直忍让是为了国家大局，深感惭愧，于是背着荆条到蔺相如家请罪。",
      explanation: "比喻主动向人认错、道歉，请求对方原谅。",
      artifacts: [
        {
          char: "荆",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "荆条束",
        },
        {
          char: "京",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "京城建筑",
        },
        {
          char: "精",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "精美器皿",
        },
        {
          char: "井",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "古井遗迹",
        },
      ],
    },
    {
      id: 5,
      idiom: "围魏救赵",
      missingChar: "赵",
      story:
        "战国时期，魏国攻打赵国，赵国向齐国求救。齐国军师孙膑建议不直接救赵，而是攻打魏国都城，迫使魏军回救，从而解除赵国之围。这一策略成功解救了赵国。",
      explanation: "比喻用包抄敌人的后方来迫使它撤兵的战术。",
      artifacts: [
        {
          char: "赵",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "赵国印章",
        },
        {
          char: "照",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "铜镜文物",
        },
        {
          char: "召",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "召集令牌",
        },
        {
          char: "兆",
          image:
            "https://picsum.photos/200/300?random=" +
            Math.floor(Math.random() * 100),
          description: "占卜甲骨",
        },
      ],
    },
  ],

  // Game settings
  settings: {
    totalQuestions: 5,
    timeLimit: 300, // 5 minutes in seconds
    pointsPerCorrect: 10,
  },

  // Badge configurations
  badges: {
    sun: {
      emoji: "☀️",
      name: "太阳徽章",
      description: "恭喜获得胜利！",
    },
    moon: {
      emoji: "🌙",
      name: "月亮徽章",
      description: "表现优秀！",
    },
  },
};

// Utility functions for game data
const gameDataUtils = {
  // Shuffle array function
  shuffleArray: function (array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  },

  // Get random questions for each player
  getRandomQuestions: function (count = 5) {
    const shuffled = this.shuffleArray(gameData.idioms);
    return shuffled.slice(0, count);
  },

  // Get shuffled artifacts for a question
  getShuffledArtifacts: function (question) {
    return this.shuffleArray(question.artifacts);
  },

  // Format time display
  formatTime: function (seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  },
};
