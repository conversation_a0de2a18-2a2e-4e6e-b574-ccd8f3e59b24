<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>邯郸文化成语竞赛 - 亲子PK</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Start Screen -->
    <div id="startScreen" class="screen active">
        <div class="start-container">
            <h1 class="game-title">邯郸文化成语竞赛</h1>
            <h2 class="game-subtitle">亲子PK模式</h2>

            <div class="rules-section">
                <h3>游戏规则</h3>
                <ul class="rules-list">
                    <li>家长和孩子分别在屏幕两端答题</li>
                    <li>根据邯郸文化文物图片选择正确的成语字符</li>
                    <li>答对得分，答错显示正确答案</li>
                    <li>所有题目必须完成，不可跳过</li>
                    <li>最后一人完成后显示结果</li>
                </ul>
            </div>

            <div class="controls-section">
                <h3>操作说明</h3>
                <p>点击文物图片选择答案，屏幕会自动翻转以便双方同时游戏</p>
            </div>

            <button id="startGameBtn" class="start-btn">开始游戏</button>
        </div>
    </div>

    <!-- Game Screen -->
    <div id="gameScreen" class="screen">


        <!-- Split Screen Game Area -->
        <div class="game-area">
            <!-- Child Section (Top, Normal) -->
            <div class="player-section child-section rotate" id="childSection">
                <div class="question-area">
                    <div class="idiom-display" id="childIdiom">
                        <span class="idiom-char">邯</span>
                        <span class="idiom-char missing" id="childMissing">?</span>
                        <span class="idiom-char">学</span>
                        <span class="idiom-char">步</span>
                    </div>
                    <div class="artifacts-grid" id="childArtifacts">
                        <!-- Artifact options will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        <!-- Central PK Bar -->
        <div class="pk-bar">
          <div class="pk-section">
            <div class="pk-progress-bar">
              <div class="pk-progress-fill child-pk-progress" id="childPkProgress"></div>
              <div class="pk-progress-fill parent-pk-progress" id="parentPkProgress"></div>
          </div>
          </div>
      </div>
            <!-- Parent Section (Bottom, Inverted) -->
            <div class="player-section parent-section" id="parentSection">
                <div class="question-area">
                    <div class="idiom-display" id="parentIdiom">
                        <span class="idiom-char">完</span>
                        <span class="idiom-char missing" id="parentMissing">?</span>
                        <span class="idiom-char">归</span>
                        <span class="idiom-char">赵</span>
                    </div>
                    <div class="artifacts-grid" id="parentArtifacts">
                        <!-- Artifact options will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Screen -->
    <div id="resultsScreen" class="screen">
        <div class="results-container">
            <h2 class="results-title">游戏结束</h2>

            <div class="final-scores">
                <div class="player-result child-result">
                    <div class="badge" id="childBadge"></div>
                    <h3>孩子</h3>
                    <div class="final-score" id="childFinalScore">0分</div>
                    <div class="completion-time" id="childTime">用时: --:--</div>
                </div>

                <div class="player-result parent-result">
                    <div class="badge" id="parentBadge"></div>
                    <h3>家长</h3>
                    <div class="final-score" id="parentFinalScore">0分</div>
                    <div class="completion-time" id="parentTime">用时: --:--</div>
                </div>
            </div>

            <div class="educational-content" id="educationalContent">
                <!-- Idiom explanations and cultural stories will be populated here -->
            </div>

            <button id="playAgainBtn" class="play-again-btn">再玩一次</button>
        </div>
    </div>

    <script src="gameData.js"></script>
    <script src="gameLogic.js"></script>
    <script src="app.js"></script>
</body>
</html>
