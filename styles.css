/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    overflow: hidden;
    touch-action: manipulation;
}

/* Screen Management */
.screen {
    display: none;
    width: 100%;
    height: 100vh;
    position: absolute;
    top: 0;
    left: 0;
}

.screen.active {
    display: flex;
    flex-direction: column;
}

/* Start Screen Styles */
#startScreen {
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.start-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    text-align: center;
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
}

.game-title {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-subtitle {
    font-size: 1.5rem;
    color: #666;
    margin-bottom: 2rem;
}

.rules-section, .controls-section {
    margin-bottom: 2rem;
    text-align: left;
}

.rules-section h3, .controls-section h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.rules-list {
    list-style: none;
    padding-left: 0;
}

.rules-list li {
    background: #f0f8ff;
    margin: 0.5rem 0;
    padding: 0.8rem;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.controls-section p {
    background: #fff3cd;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #ffc107;
    color: #856404;
}

.start-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 1rem 3rem;
    font-size: 1.5rem;
    border-radius: 50px;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
    transition: all 0.3s ease;
    font-weight: bold;
}

.start-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6);
}

.start-btn:active {
    transform: translateY(0);
}

/* Game Screen Styles */
#gameScreen {
    background: #f5f5f5;
}

/* Score and Timer Bar */
.score-timer-bar {
    background: rgba(255, 255, 255, 0.95);
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.score-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 800px;
    margin: 0 auto;
}

.player-score {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.player-label {
    font-weight: bold;
    font-size: 1.1rem;
    min-width: 3rem;
}

.progress-bar {
    flex: 1;
    height: 20px;
    background: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    max-width: 200px;
}

.progress-fill {
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.child-progress {
    background: linear-gradient(45deg, #ff9a9e, #fecfef);
}

.parent-progress {
    background: linear-gradient(45deg, #a8edea, #fed6e3);
}

.score-text {
    font-weight: bold;
    min-width: 3rem;
    text-align: center;
}

.timer {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    background: #fff;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Game Area */
.game-area {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.player-section {
    flex: 1;
    padding: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Child Section (Top, Inverted) */
.child-section {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    transform: rotate(180deg);
    position: relative;
}

.child-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%23ff9a9e" opacity="0.3"/><circle cx="80" cy="30" r="1.5" fill="%23fecfef" opacity="0.4"/><circle cx="60" cy="70" r="2.5" fill="%23ff9a9e" opacity="0.2"/><circle cx="30" cy="80" r="1" fill="%23fecfef" opacity="0.5"/></svg>') repeat;
    pointer-events: none;
}

.child-section .question-area {
    transform: rotate(180deg);
    position: relative;
    z-index: 10;
}

/* Parent Section (Bottom, Normal) */
.parent-section {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    position: relative;
}

.parent-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><polygon points="10,10 20,5 30,15 15,20" fill="%23a8edea" opacity="0.3"/><polygon points="70,20 85,15 90,30 75,35" fill="%23fed6e3" opacity="0.2"/><polygon points="40,60 55,55 60,70 45,75" fill="%23a8edea" opacity="0.4"/></svg>') repeat;
    pointer-events: none;
}

/* Question Area */
.question-area {
    width: 100%;
    max-width: 600px;
    text-align: center;
}

.idiom-display {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.idiom-char {
    width: 4rem;
    height: 4rem;
    background: white;
    border: 3px solid #333;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.idiom-char.missing {
    background: #ffeb3b;
    border-color: #ff9800;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Artifacts Grid */
.artifacts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.artifact-option {
    background: white;
    border: 3px solid transparent;
    border-radius: 15px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.artifact-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.artifact-option:hover::before {
    left: 100%;
}

.artifact-option:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    border-color: #667eea;
}

.artifact-option.selected {
    border-color: #4caf50;
    background: #e8f5e8;
    animation: correctPulse 0.6s ease;
}

.artifact-option.wrong {
    border-color: #f44336;
    background: #ffebee;
    animation: wrongShake 0.6s ease;
}

@keyframes correctPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes wrongShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.artifact-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 0.5rem;
}

.artifact-char {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
}

/* Feedback */
.feedback {
    min-height: 2rem;
    font-size: 1.2rem;
    font-weight: bold;
    padding: 0.5rem;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.feedback.correct {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.feedback.incorrect {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Results Screen */
#resultsScreen {
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.results-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    text-align: center;
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
}

.results-title {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 2rem;
}

.final-scores {
    display: flex;
    justify-content: space-around;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 2rem;
}

.player-result {
    text-align: center;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    min-width: 200px;
}

.child-result {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.parent-result {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.badge {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.badge.sun {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
}

.badge.moon {
    background: linear-gradient(45deg, #c0c0c0, #e8e8e8);
    color: #333;
}

.final-score {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #333;
}

.completion-time {
    font-size: 1.1rem;
    color: #666;
}

.educational-content {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    text-align: left;
    max-height: 300px;
    overflow-y: auto;
}

.play-again-btn {
    background: linear-gradient(45deg, #4caf50, #45a049);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.3rem;
    border-radius: 50px;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
    transition: all 0.3s ease;
    font-weight: bold;
}

.play-again-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.6);
}

/* Touch and Mobile Enhancements */
.artifact-option {
    -webkit-tap-highlight-color: transparent;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.artifact-option:active {
    transform: translateY(-2px) scale(0.98);
}

/* Prevent zoom on double tap */
button, .artifact-option {
    touch-action: manipulation;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .game-title {
        font-size: 2rem;
    }

    .idiom-char {
        width: 3rem;
        height: 3rem;
        font-size: 1.5rem;
        gap: 0.5rem;
    }

    .idiom-display {
        gap: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .artifacts-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.8rem;
    }

    .artifact-image {
        width: 60px;
        height: 60px;
    }

    .artifact-option {
        padding: 0.8rem;
    }

    .final-scores {
        flex-direction: column;
        align-items: center;
    }

    .score-section {
        flex-direction: column;
        gap: 1rem;
    }

    .player-score {
        justify-content: center;
    }

    .player-section {
        padding: 0.8rem;
    }

    .score-timer-bar {
        padding: 0.8rem;
    }
}

@media (max-width: 480px) {
    .start-container, .results-container {
        padding: 1rem;
        margin: 1rem;
    }

    .game-title {
        font-size: 1.5rem;
    }

    .game-subtitle {
        font-size: 1.2rem;
    }

    .idiom-char {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1.2rem;
    }

    .idiom-display {
        gap: 0.3rem;
    }

    .artifact-image {
        width: 50px;
        height: 50px;
    }

    .artifact-char {
        font-size: 1.2rem;
    }

    .artifacts-grid {
        gap: 0.5rem;
    }

    .artifact-option {
        padding: 0.6rem;
    }

    .timer {
        font-size: 1.2rem;
        padding: 0.3rem 0.8rem;
    }

    .progress-bar {
        height: 16px;
        max-width: 150px;
    }

    .player-label, .score-text {
        font-size: 0.9rem;
    }
}

/* Landscape orientation adjustments */
@media (max-height: 600px) and (orientation: landscape) {
    .start-container, .results-container {
        max-height: 95vh;
        padding: 1rem;
    }

    .game-title {
        font-size: 1.8rem;
        margin-bottom: 0.5rem;
    }

    .rules-section, .controls-section {
        margin-bottom: 1rem;
    }

    .idiom-char {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1.2rem;
    }

    .artifacts-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 0.5rem;
    }

    .artifact-image {
        width: 40px;
        height: 40px;
    }

    .player-section {
        padding: 0.5rem;
    }
}
